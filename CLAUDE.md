# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

The project uses Vite as the build tool with the following npm scripts:

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production (outputs to `dist/`)  
- `npm run lint` - Run ESLint to check code quality
- `npm run preview` - Preview the production build locally

## Architecture Overview

This is a React-based observability dashboard application with the following architecture:

### Tech Stack
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with PostCSS
- **Icons**: Lucide React
- **Linting**: ESLint with TypeScript and React plugins

### Application Structure
- **Single Page Application**: The main component is `App.tsx` which renders an observability exploration interface
- **Component-based Architecture**: Uses functional React components with hooks
- **No External State Management**: Uses built-in React state (useState, useEffect)
- **Mock Data**: Currently uses hardcoded mock data for services, metrics, and insights

### Key Features
- **Service Selection**: Multi-service selection for comparative analysis
- **AI Chat Interface**: Simulated AI assistant for data exploration with conversational UI
- **Real-time Metrics**: Live system metrics display (CPU, memory, network, etc.)
- **Quick Actions**: Predefined exploration patterns for common observability tasks
- **Insights Panel**: AI-generated insights with confidence scores
- **Responsive Design**: Mobile-friendly interface using Tailwind utilities

### Core Data Models
- `Service`: Represents monitored services with metrics, status, and metadata
- `ExplorationStep`: Chat conversation steps with queries and AI responses  
- `Insight`: AI-generated observations with confidence levels and categorization
- `QuickAction`: Predefined exploration templates

### File Organization
```
src/
├── App.tsx          # Main application component
├── main.tsx         # React app entry point
├── index.css        # Tailwind CSS imports
└── vite-env.d.ts    # Vite type definitions
```

The application is a prototype/demo of an AI-powered observability platform with a focus on conversational data exploration rather than real monitoring capabilities.