import React, { useState, useRef, useEffect } from 'react';
import { 
  MessageCircle, 
  BarChart3, 
  Search,
  Send,
  TrendingUp,
  Activity,
  Zap,
  Eye,
  Brain,
  Lightbulb,
  Target,
  Compass,
  ChevronRight,
  Play,
  Pause,
  RotateCcw,
  Bookmark,
  Share2,
  Download,
  Filter,
  Calendar,
  Clock,
  Database,
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowRight,
  Sparkles,
  GitBranch,
  Map,
  Layers,
  MousePointer,
  Cpu,
  HardDrive,
  Network,
  Globe
} from 'lucide-react';

interface ExplorationStep {
  id: string;
  query: string;
  response: string;
  data?: any;
  timestamp: Date;
  suggestions?: string[];
  visualizations?: any[];
}

interface Insight {
  id: string;
  title: string;
  description: string;
  confidence: number;
  type: 'anomaly' | 'trend' | 'correlation' | 'prediction';
  data: any;
  timestamp: Date;
}

interface QuickAction {
  id: string;
  label: string;
  description: string;
  icon: any;
  category: string;
}

interface Service {
  id: string;
  name: string;
  type: 'web' | 'api' | 'database' | 'cache' | 'queue' | 'gateway';
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  environment: 'production' | 'staging' | 'development';
  version: string;
  instances: number;
  metrics: {
    cpu: number;
    memory: number;
    requests: string;
    errors: string;
    latency: string;
  };
}
const App: React.FC = () => {
  const [selectedServices, setSelectedServices] = useState<string[]>(['all']);
  const [services] = useState<Service[]>([
    {
      id: 'user-service',
      name: '用户服务',
      type: 'api',
      status: 'healthy',
      environment: 'production',
      version: 'v2.1.3',
      instances: 3,
      metrics: { cpu: 45, memory: 62, requests: '2.1K/s', errors: '0.12%', latency: '89ms' }
    },
    {
      id: 'payment-service',
      name: '支付服务',
      type: 'api',
      status: 'warning',
      environment: 'production',
      version: 'v1.8.2',
      instances: 5,
      metrics: { cpu: 78, memory: 84, requests: '1.8K/s', errors: '0.45%', latency: '156ms' }
    },
    {
      id: 'order-service',
      name: '订单服务',
      type: 'api',
      status: 'healthy',
      environment: 'production',
      version: 'v3.0.1',
      instances: 4,
      metrics: { cpu: 52, memory: 68, requests: '1.5K/s', errors: '0.08%', latency: '112ms' }
    },
    {
      id: 'notification-service',
      name: '通知服务',
      type: 'queue',
      status: 'healthy',
      environment: 'production',
      version: 'v1.5.0',
      instances: 2,
      metrics: { cpu: 23, memory: 41, requests: '850/s', errors: '0.02%', latency: '45ms' }
    },
    {
      id: 'web-gateway',
      name: 'Web网关',
      type: 'gateway',
      status: 'healthy',
      environment: 'production',
      version: 'v2.3.1',
      instances: 6,
      metrics: { cpu: 38, memory: 55, requests: '5.2K/s', errors: '0.15%', latency: '67ms' }
    },
    {
      id: 'main-database',
      name: '主数据库',
      type: 'database',
      status: 'warning',
      environment: 'production',
      version: 'PostgreSQL 14.2',
      instances: 1,
      metrics: { cpu: 72, memory: 89, requests: '3.2K/s', errors: '0.01%', latency: '234ms' }
    },
    {
      id: 'redis-cache',
      name: 'Redis缓存',
      type: 'cache',
      status: 'healthy',
      environment: 'production',
      version: 'Redis 7.0',
      instances: 3,
      metrics: { cpu: 15, memory: 34, requests: '8.5K/s', errors: '0.00%', latency: '2ms' }
    }
  ]);

  const [explorationSteps, setExplorationSteps] = useState<ExplorationStep[]>([
    {
      id: '1',
      query: '',
      response: '👋 欢迎来到可观测数据探索中心！我是您的AI助手，让我们一起探索您的系统数据。\n\n**开始探索前，请选择要分析的服务：**\n• 选择特定服务进行深入分析\n• 或选择"全部服务"进行整体概览\n• 可以多选服务进行对比分析\n\n**您可以询问：**\n• "支付服务的响应时间为什么这么高？"\n• "用户服务和订单服务的错误率对比如何？"\n• "数据库的性能瓶颈在哪里？"',
      timestamp: new Date(),
      suggestions: [
        '支付服务为什么出现警告状态？',
        '对比各个服务的性能表现',
        '数据库的性能瓶颈分析',
        '哪些服务需要扩容？'
      ]
    }
  ]);
  
  const [currentQuery, setCurrentQuery] = useState('');
  const [isExploring, setIsExploring] = useState(false);
  const [insights, setInsights] = useState<Insight[]>([
    {
      id: '1',
      title: 'CPU使用率异常峰值',
      description: '检测到生产环境CPU使用率在过去2小时内出现3次异常峰值，可能与新部署相关',
      confidence: 0.87,
      type: 'anomaly',
      data: { metric: 'cpu_usage', threshold: 85, current: 92 },
      timestamp: new Date(Date.now() - 300000)
    },
    {
      id: '2',
      title: '响应时间趋势上升',
      description: '数据库查询响应时间呈现上升趋势，建议检查索引优化',
      confidence: 0.73,
      type: 'trend',
      data: { metric: 'response_time', trend: 'increasing', rate: 0.15 },
      timestamp: new Date(Date.now() - 600000)
    },
    {
      id: '3',
      title: '流量预测',
      description: '基于历史数据，预测明天下午2-4点将出现流量高峰',
      confidence: 0.91,
      type: 'prediction',
      data: { metric: 'traffic', predicted_peak: '14:00-16:00', increase: '45%' },
      timestamp: new Date(Date.now() - 900000)
    }
  ]);

  const [quickActions] = useState<QuickAction[]>([
    { id: '1', label: '性能概览', description: '查看系统整体性能状况', icon: Activity, category: 'overview' },
    { id: '2', label: '异常检测', description: '发现系统中的异常模式', icon: Target, category: 'analysis' },
    { id: '3', label: '趋势分析', description: '分析关键指标的变化趋势', icon: TrendingUp, category: 'analysis' },
    { id: '4', label: '关联分析', description: '探索指标间的相关性', icon: GitBranch, category: 'analysis' },
    { id: '5', label: '容量规划', description: '预测资源需求和容量', icon: Compass, category: 'planning' },
    { id: '6', label: '根因分析', description: '深入分析问题根本原因', icon: Search, category: 'troubleshooting' }
  ]);

  const [liveMetrics] = useState({
    cpu: { value: 68, trend: 'up', change: '+5%' },
    memory: { value: 72, trend: 'stable', change: '+1%' },
    network: { value: 45, trend: 'down', change: '-8%' },
    disk: { value: 34, trend: 'stable', change: '+2%' },
    requests: { value: '1.2K/s', trend: 'up', change: '+12%' },
    errors: { value: '0.23%', trend: 'up', change: '+15%' },
    latency: { value: '245ms', trend: 'down', change: '-8%' },
    availability: { value: '99.97%', trend: 'stable', change: '0%' }
  });

  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [explorationSteps]);

  const handleExplore = async (query?: string) => {
    const queryText = query || currentQuery;
    if (!queryText.trim()) return;

    const newStep: ExplorationStep = {
      id: Date.now().toString(),
      query: queryText,
      response: '',
      timestamp: new Date()
    };

    setExplorationSteps(prev => [...prev, newStep]);
    setCurrentQuery('');
    setIsExploring(true);

    // 模拟AI分析过程
    setTimeout(() => {
      const responses = [
        {
          response: `🔍 正在分析CPU使用率数据...\n\n**发现的异常模式：**\n• 在14:23-14:45期间，CPU使用率从65%激增至92%\n• 异常持续了22分钟，超出正常波动范围\n• 与同时间段的内存使用率上升呈正相关\n\n**可能原因：**\n1. 新部署的服务存在性能问题\n2. 数据库查询效率下降\n3. 缓存命中率降低\n\n**建议行动：**\n• 检查最近的代码部署\n• 分析慢查询日志\n• 监控缓存性能指标`,
          suggestions: [
            '帮我查看最近的部署记录',
            '分析一下数据库慢查询',
            '检查缓存命中率趋势',
            '对比其他服务器的CPU使用情况'
          ]
        },
        {
          response: `📈 响应时间趋势分析完成！\n\n**趋势概况：**\n• 过去24小时平均响应时间：245ms\n• 相比昨天同期上升了18%\n• P95响应时间达到了580ms\n\n**关键发现：**\n• 数据库查询时间占总响应时间的65%\n• API网关处理时间增加了12%\n• 第三方服务调用延迟上升\n\n**优化建议：**\n1. 优化数据库索引策略\n2. 实施查询结果缓存\n3. 考虑数据库连接池调优`,
          suggestions: [
            '显示最慢的API端点',
            '分析数据库连接池状态',
            '检查第三方服务依赖',
            '生成性能优化报告'
          ]
        },
        {
          response: `⚠️ 错误率分析结果：\n\n**当前状况：**\n• 整体错误率：0.23%（正常范围：<0.1%）\n• 主要错误类型：数据库连接超时(45%)、API限流(30%)\n• 受影响的服务：用户认证服务、支付服务\n\n**上升趋势：**\n• 过去2小时错误率上升了15%\n• 支付服务错误率达到0.8%\n• 用户投诉增加了3倍\n\n**紧急建议：**\n1. 立即检查数据库连接池配置\n2. 调整API限流策略\n3. 启动故障应急预案`,
          suggestions: [
            '查看详细错误日志',
            '检查服务健康状态',
            '启动自动故障转移',
            '通知运维团队'
          ]
        }
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      setExplorationSteps(prev => 
        prev.map(step => 
          step.id === newStep.id 
            ? { ...step, ...randomResponse }
            : step
        )
      );
      setIsExploring(false);
    }, 2500);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleExplore(suggestion);
  };

  const handleQuickAction = (action: QuickAction) => {
    const actionQueries = {
      '1': '给我一个系统性能的全面概览',
      '2': '帮我检测系统中的异常模式',
      '3': '分析关键指标的变化趋势',
      '4': '探索不同指标之间的关联性',
      '5': '基于当前使用情况预测容量需求',
      '6': '帮我分析最近问题的根本原因'
    };
    handleExplore(actionQueries[action.id as keyof typeof actionQueries]);
  };

  const getInsightIcon = (type: Insight['type']) => {
    switch (type) {
      case 'anomaly': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      case 'trend': return <TrendingUp className="w-5 h-5 text-blue-400" />;
      case 'correlation': return <GitBranch className="w-5 h-5 text-emerald-400" />;
      case 'prediction': return <Sparkles className="w-5 h-5 text-green-400" />;
      default: return <Lightbulb className="w-5 h-5 text-yellow-400" />;
    }
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'cpu': return <Cpu className="w-5 h-5" />;
      case 'memory': return <HardDrive className="w-5 h-5" />;
      case 'network': return <Network className="w-5 h-5" />;
      case 'disk': return <Database className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-red-400';
      case 'down': return 'text-green-400';
      case 'stable': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getServiceIcon = (type: Service['type']) => {
    switch (type) {
      case 'web': return <Globe className="w-4 h-4" />;
      case 'api': return <Server className="w-4 h-4" />;
      case 'database': return <Database className="w-4 h-4" />;
      case 'cache': return <Zap className="w-4 h-4" />;
      case 'queue': return <Layers className="w-4 h-4" />;
      case 'gateway': return <Network className="w-4 h-4" />;
      default: return <Server className="w-4 h-4" />;
    }
  };

  const getServiceStatusColor = (status: Service['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-400 bg-green-500/10 border-green-500/20';
      case 'warning': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';
      case 'critical': return 'text-red-400 bg-red-500/10 border-red-500/20';
      case 'unknown': return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
    }
  };

  const getServiceStatusText = (status: Service['status']) => {
    switch (status) {
      case 'healthy': return '健康';
      case 'warning': return '警告';
      case 'critical': return '严重';
      case 'unknown': return '未知';
      default: return '未知';
    }
  };

  const handleServiceToggle = (serviceId: string) => {
    if (serviceId === 'all') {
      setSelectedServices(['all']);
    } else {
      setSelectedServices(prev => {
        const newSelection = prev.filter(id => id !== 'all');
        if (newSelection.includes(serviceId)) {
          const filtered = newSelection.filter(id => id !== serviceId);
          return filtered.length === 0 ? ['all'] : filtered;
        } else {
          return [...newSelection, serviceId];
        }
      });
    }
  };

  const getSelectedServicesContext = () => {
    if (selectedServices.includes('all')) {
      return '全部服务';
    }
    const selectedServiceNames = services
      .filter(service => selectedServices.includes(service.id))
      .map(service => service.name);
    return selectedServiceNames.join('、');
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-emerald-50 to-gray-50">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-md border-b border-emerald-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Brain className="w-8 h-8 text-emerald-600" />
                <span className="ml-2 text-xl font-bold text-gray-800">ObservabilityExplorer</span>
              </div>
              <div className="hidden md:flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span>AI协同探索模式</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button className="flex items-center space-x-2 text-gray-600 hover:text-emerald-600 transition-colors">
                <Bookmark className="w-5 h-5" />
                <span className="hidden md:inline">保存探索</span>
              </button>
              <button className="flex items-center space-x-2 text-gray-600 hover:text-emerald-600 transition-colors">
                <Share2 className="w-5 h-5" />
                <span className="hidden md:inline">分享</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Exploration Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Service Selection */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                  <Layers className="w-5 h-5 mr-2 text-emerald-600" />
                  服务选择
                </h2>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MousePointer className="w-4 h-4" />
                  <span>当前分析范围：{getSelectedServicesContext()}</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
                {/* All Services Option */}
                <button
                  onClick={() => handleServiceToggle('all')}
                  className={`p-3 rounded-lg border transition-all duration-200 ${
                    selectedServices.includes('all')
                      ? 'bg-emerald-50 border-emerald-400 text-emerald-700'
                      : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-emerald-50 hover:border-emerald-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <Globe className="w-4 h-4" />
                    <span className="font-medium">全部服务</span>
                  </div>
                  <p className="text-xs mt-1 opacity-75">整体系统分析</p>
                </button>

                {/* Individual Services */}
                {services.map((service) => (
                  <button
                    key={service.id}
                    onClick={() => handleServiceToggle(service.id)}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      selectedServices.includes(service.id)
                        ? 'bg-emerald-50 border-emerald-400 text-emerald-700'
                        : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-emerald-50 hover:border-emerald-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getServiceIcon(service.type)}
                        <span className="font-medium text-sm">{service.name}</span>
                      </div>
                      <span className={`px-2 py-0.5 rounded text-xs border ${getServiceStatusColor(service.status)}`}>
                        {getServiceStatusText(service.status)}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      <div className="text-left">
                        <span className="text-gray-500">CPU:</span>
                        <span className="ml-1 text-gray-800">{service.metrics.cpu}%</span>
                      </div>
                      <div className="text-left">
                        <span className="text-gray-500">延迟:</span>
                        <span className="ml-1 text-gray-800">{service.metrics.latency}</span>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
              
              {!selectedServices.includes('all') && selectedServices.length > 1 && (
                <div className="bg-emerald-50 border-2 border-emerald-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2 text-emerald-700">
                    <GitBranch className="w-4 h-4" />
                    <span className="text-sm font-medium">多服务对比模式</span>
                  </div>
                  <p className="text-xs text-emerald-600 mt-1">
                    已选择 {selectedServices.length} 个服务进行对比分析，AI将提供跨服务的关联洞察
                  </p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                  <Compass className="w-5 h-5 mr-2 text-emerald-600" />
                  快速探索
                </h2>
                <span className="text-sm text-gray-600">选择探索方向</span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {quickActions.map((action) => (
                  <button
                    key={action.id}
                    onClick={() => handleQuickAction(action)}
                    className="flex items-center space-x-3 p-3 bg-gray-50 hover:bg-emerald-50 border border-gray-200 hover:border-emerald-300 rounded-lg transition-all duration-200 group"
                  >
                    <action.icon className="w-5 h-5 text-emerald-600 group-hover:text-emerald-700" />
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-800">{action.label}</p>
                      <p className="text-xs text-gray-600">{action.description}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Exploration Chat */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 flex flex-col shadow-sm" style={{ height: '600px' }}>
              <div className="flex items-center justify-between p-4 border-b-2 border-emerald-100">
                <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                  <MessageCircle className="w-5 h-5 mr-2 text-emerald-600" />
                  协同探索对话
                </h2>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-emerald-600">AI助手在线</span>
                </div>
              </div>
              
              <div className="flex-1 overflow-y-auto p-4 space-y-6">
                {explorationSteps.map((step, index) => (
                  <div key={step.id} className="space-y-4">
                    {step.query && (
                      <div className="flex justify-end">
                        <div className="max-w-2xl bg-emerald-50 border-2 border-emerald-200 rounded-lg p-4">
                          <p className="text-gray-800">{step.query}</p>
                          <div className="text-xs text-emerald-600 mt-2">
                            {step.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex justify-start">
                      <div className="max-w-4xl bg-gray-50 border-2 border-gray-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <Brain className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1">
                            {step.response ? (
                              <div className="prose prose-invert max-w-none">
                                <div className="text-gray-800 whitespace-pre-line">{step.response}</div>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2">
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce"></div>
                                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                                <span className="text-gray-600 text-sm">AI正在分析数据...</span>
                              </div>
                            )}
                            
                            {step.suggestions && step.suggestions.length > 0 && (
                              <div className="mt-4 space-y-2">
                                <p className="text-sm text-gray-600">💡 建议的下一步探索：</p>
                                <div className="flex flex-wrap gap-2">
                                  {step.suggestions.map((suggestion, idx) => (
                                    <button
                                      key={idx}
                                      onClick={() => handleSuggestionClick(suggestion)}
                                      className="text-sm bg-emerald-100 hover:bg-emerald-200 text-emerald-700 border border-emerald-300 px-3 py-1 rounded-full transition-colors flex items-center space-x-1"
                                    >
                                      <span>{suggestion}</span>
                                      <ArrowRight className="w-3 h-3" />
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={chatEndRef} />
              </div>
              
              <div className="p-4 border-t-2 border-emerald-100">
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={currentQuery}
                    onChange={(e) => setCurrentQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !isExploring && handleExplore()}
                    placeholder={`针对${getSelectedServicesContext()}，描述您想探索的问题...`}
                    className="flex-1 bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-400"
                    disabled={isExploring}
                  />
                  <button
                    onClick={() => handleExplore()}
                    disabled={isExploring || !currentQuery.trim()}
                    className="bg-emerald-500 hover:bg-emerald-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2 border-2 border-emerald-500 hover:border-emerald-600"
                  >
                    {isExploring ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>探索中</span>
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        <span>探索</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Selected Services Overview */}
            {!selectedServices.includes('all') && (
              <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-emerald-600" />
                  选中服务概览
                </h3>
                <div className="space-y-3">
                  {services
                    .filter(service => selectedServices.includes(service.id))
                    .map((service) => (
                      <div key={service.id} className="bg-gray-50 rounded-lg p-3 border-2 border-gray-200">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {getServiceIcon(service.type)}
                            <span className="text-sm font-medium text-gray-800">{service.name}</span>
                          </div>
                          <span className={`px-2 py-0.5 rounded text-xs border ${getServiceStatusColor(service.status)}`}>
                            {getServiceStatusText(service.status)}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500">请求:</span>
                            <span className="ml-1 text-gray-800">{service.metrics.requests}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">错误:</span>
                            <span className="ml-1 text-gray-800">{service.metrics.errors}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Live Metrics */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-emerald-600" />
                实时指标
              </h3>
              <div className="space-y-4">
                {Object.entries(liveMetrics).slice(0, 4).map(([key, metric]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getMetricIcon(key)}
                      <div>
                        <p className="text-sm text-gray-600 capitalize">{key}</p>
                        <p className="text-lg font-semibold text-gray-800">{metric.value}</p>
                      </div>
                    </div>
                    <div className={`text-sm ${getTrendColor(metric.trend)}`}>
                      {metric.change}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* AI Insights */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-400" />
                AI洞察
              </h3>
              <div className="space-y-4">
                {insights.map((insight) => (
                  <div key={insight.id} className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                    <div className="flex items-start space-x-3">
                      {getInsightIcon(insight.type)}
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-800 mb-1">{insight.title}</h4>
                        <p className="text-xs text-gray-600 mb-2">{insight.description}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-full bg-gray-200 rounded-full h-1.5">
                              <div 
                                className="bg-emerald-500 h-1.5 rounded-full" 
                                style={{ width: `${insight.confidence * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-600">{Math.round(insight.confidence * 100)}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Exploration History */}
            <div className="bg-white rounded-xl border-2 border-emerald-200 p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Map className="w-5 h-5 mr-2 text-emerald-600" />
                探索历史
              </h3>
              <div className="space-y-3">
                {explorationSteps.slice(-3).reverse().map((step, index) => (
                  step.query && (
                    <div key={step.id} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 font-medium line-clamp-2">{step.query}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          {step.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  )
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;